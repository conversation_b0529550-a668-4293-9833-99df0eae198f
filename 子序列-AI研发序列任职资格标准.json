{"AI工程序列定义": {"AI应用架构": {"定义": "基于对AI技术动态和行业趋势洞察，结合业务需求，整合已有AI技术或平台，规划SaaS级AI应用系统，开展架构设计，推进研发工作，主导应用系统落地，降低AI应用接入的门槛；\n对标行业竞品，持续规划和迭代系统（功能完善性、技术先进性、交付速度、易用性、质量等），提升系统的竞争力，关注数据回流及核心效果迭代优化，促进AI外循环，为客户创造更高的价值；\n平衡共性需求与定制需求，提升系统架构的兼容性、扩展性；\n在系统的规模化过程中，提前识别系统瓶颈，推进优化系统性能、稳定性，降低风险；", "职位": ["高级AI应用架构师", "资深AI应用架构师"]}, "AI平台架构": {"定义": "基于对AI工程技术趋势洞察，结合AI生产落地过程中的痛点（效率、成本、性能、稳定性等），规划PaaS级AI平台（AI生产过程工具链平台化、AI算法&能力平台化、AI应用系统中共性模块平台化等），开展架构设计，推进研发及落地，解决主要痛点问题；\n对标行业竞品，结合业务需求，持续规划和迭代平台，提升关键指标，加速AI内循环，提升平台竞争力；", "职位": ["高级AI平台架构师", "资深AI平台架构师"]}, "AI研发": {"定义": "基于对AI技术的深刻理解以及工程的深入实践，配合AI应用系统或AI平台架构师进行架构设计，并主导系统的研发工作，攻克工程难点，保障系统高效、平稳落地，实现快速交付；\n根据业务以及系统演进需求，主导大版本的规划与迭代，主导系统关键指标提升的研发工作，并建立高效可靠的系统维护保障措施；\n持续关注AI相关软件工程开源技术动态，适时引进关键技术改进系统，提升系统竞争力。通过工程实践的积累，逐步沉淀AI工程领域的关键工具、模块、框架，贡献给开源社区，逐步形成技术影响力；", "职位": ["助理AI研发工程师", "助理AI应用工程师", "初级AI研发工程师", "初级AI应用工程师", "中级AI研发工程师", "中级AI应用工程师", "高级AI研发工程师", "资深AI研发工程师"]}}, "序列标准编制的锚定标准": [{"任职资格等级": "六级", "角色": "行业专家", "定位": "【变革引领且绝对领先】序列专业能力处于行业领先地位的主责主导者", "资格标准要求（满足其一即可）": ["1.1 引领序列发展方向，提出具有战略性的指导思想，主导并达成序列业务指标或运作模式的全球领先水平；", "1.2 通过行业研究或专业视野，在序列内取得远超竞争对手的工作成果（有第三方数据），或具备突出的商业化前景，实现公司序列专业能力与主要竞争对手形成代差优势。"], "必备知识要求": "/"}, {"任职资格等级": "五级", "角色": "公司专家", "定位": "【重大变革/迭代升级】序列创新变革的主责主导者，能够解决序列层面重大、复杂问题", "资格标准要求（满足其一即可）": ["1.1 把握序列发展趋势，通过系统专业的方法，解决序列或某一子序列重大或疑难问题，形成质的突破，实现业务运作模式或管理模式创新，且为公司或BG/BU/平台战略任务主责主导者；", "1.2 在技术方面取得重大突破或创造重大价值贡献，实现效能的大幅提升（可衡量）。"], "必备知识要求": "精通序列内多个子序列的知识"}, {"任职资格等级": "四级", "角色": "序列骨干", "定位": "【重大创新/优化】子序列业务对标改善的主责主导者", "资格标准要求（满足其一即可）": ["把控子序列多模块运营状态，通过对标分析找差距、定方向，制定系统的策划方案并主导实施，实现子序列运营上台阶，且为BG/BU/平台重点工作主责主导者（或同等价值事项）。"], "必备知识要求": "精通某一子序列多模块的知识，熟悉相关子序列的知识"}, {"任职资格等级": "三级", "角色": "有经验者", "定位": "【点状创新/优化】子序列某一模块业务闭环管理（PDCA）的主责主导者", "资格标准要求（满足其一即可）": ["把控子序列某一模块运营状态，通过独立、熟练地完成例行业务的闭环管理（PDCA），过程发现问题及时纠正，实现其良好运作，且能指导初做者开展例行性工作。"], "必备知识要求": "掌握子序列的知识，且在某一模块是精通的"}, {"任职资格等级": "二级", "角色": "初做者", "定位": "在例行情况下，子序列某一模块业务能够独立运作", "资格标准要求（满足其一即可）": ["根据制度/流程要求，通过独立完成例行业务的全流程运作，能发现一般问题并提供初步建议。"], "必备知识要求": "掌握子序列某一模块的知识"}], "子序列-AI研发序列任职资格标准": [{"业务单元": "技术调研和洞察", "活动类别": [{"活动类别": "(目标确定的)技术调研", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "能够负责某系统的关键模块技术调研，调研成果对项目的技术决策起到一定的影响", "成果要求": ["输出关键模块的调研报告", "决策影响说明"]}, "四级": {"任职资格标准": "能够负责和主导AI工程领域某方向的关键及核心技术调研，调研成果对某重大方向的研发和技术决策起到决定性作用", "成果要求": ["输出技术调研报告", "决策影响说明，提供决策相关的材料或记录，体现调研信息的作用"]}, "五级": {"任职资格标准": "能够负责和主导AI工程领域多个方向的核心技术调研，调研成果对一级部门重大投入决策起到决定性作用，调研结果直接影响技术决策被采用，并取得了较好效果", "成果要求": ["输出技术调研报告", "决策影响说明，提供决策相关的材料或记录，体现调研信息的作用", "效果说明"]}}}, {"活动类别": "(开放的)技术洞察", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "四级": {"任职资格标准": "能够主动持续跟踪AI工程领域某些子方向技术动态，及时评估其影响，能够针对该方向影响深远的技术趋势及时输出技术洞察报告，该报告可用于指导技术调研", "成果要求": ["输出技术洞察报告", "输出被技术洞察报告指导的技术调研报告", "所洞悉的技术必须发展阶段的新技术，并不完全成熟，在众多的技术路线中并不占主导性优势。"]}, "五级": {"任职资格标准": "能够主动持续跟踪AI工程领域技术动态并做出技术趋势预判，能够对影响深远的技术趋势及时做出洞察报告，该报告可用于指导技术调研", "成果要求": ["输出技术洞察报告", "输出被技术洞察报告指导的技术调研报告", "所洞悉的技术必须发展阶段的新技术，并不完全成熟，在众多的技术路线中并不占主导性优势。"]}}}]}, {"业务单元": "需求", "活动类别": [{"活动类别": "需求分析与确认", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "参与模块级的需求确认工作，针对可能存在的问题或风险进行确认，形成结论", "成果要求": ["需描述针对需求和设计中描述不清晰或者存在风险等与需求和设计人员相关确认信息（如文档、邮件等）"]}, "四级": {"任职资格标准": "能够在中/小型项目中，主动收集并梳理AI研发需求与高阶人员协同，独立完成关键技术点分析，评估可行性和成本，参与方案决策并提出有效意见", "成果要求": ["技术评估报告，说明关键技术的挑战和风险，需要举证协同条线共同评估的闭环过程", "关键技术是指对系统成败有巨大影响，具有相当的技术难度和挑战"]}, "五级": {"任职资格标准": "能够在大/中型项目中，主动收集并梳理重大AI研发需求，与产品经理协同进行用户需求与场景分析，充分识别功能性需求和非功能性需求，将业务需求转换为技术需求，对需求的实现成本进行有效的确认，制定合理的技术侧和产品侧指标", "成果要求": ["需求分析结果，包括分析后的产品功能和技术指标"]}}}, {"活动类别": "风险控制", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "能够运用经验识别研发需求中的一般技术风险，主导制定应对方案并推进落地，成功避免风险和问题发生", "成果要求": ["举证技术风险，以及技术调研报告或评估报告，以及最终的解决方案和应用成果"]}, "四级": {"任职资格标准": "能够识别出中型项目的重大技术风险，制定应对方案并推进落地，在项目早期化解风险", "成果要求": ["举证风险消降方案及后续执行效果分析"]}, "五级": {"任职资格标准": "能够识别出大型项目（或BG/BU级核心系统）的致命技术风险，制定风险消除方案并推进落地，避免风险发生或在项目早期化解风险", "成果要求": ["举证风险的消降方案及后续执行效果分析"]}}}]}, {"业务单元": "设计", "活动类别": [{"活动类别": "系统和方案设计与迭代", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "基于业务需求、架构设计、非功能需求等材料，完成模块级详细设计，并通过上级或专家评审。\n\n标准解释（认证方法/依据）：\n1、详细设计需要符合集团规范要求。\n2、达到模块级/一般复杂度\n3、举证评审和问题解决闭环", "成果要求": ["详细设计需要符合集团规范要求，举证自己参与的部分", "重点阐述难度/复杂度的成因及设计考量", "举证评审和问题解决闭环"]}, "四级": {"任职资格标准": "能够在高阶人员有限指导下，参与项目的架构设计，主导完成子系统详细设计，并通过专家评审\n\n【成果要求】\n1、设计需要符合集团规范要求，举证自己参与的部分\n2、重点阐述难度/复杂度的成因及设计考量\n3、举证评审和问题解决闭环", "成果要求": ["设计需要符合集团规范要求，举证自己参与的部分", "重点阐述难度/复杂度的成因及设计考量"]}, "五级": {"任职资格标准": "能够配合架构师，完成中型项目的架构设计，主导完成关键子系统详细设计，并通过专家评审\n\n\n\n【成果要求】\n1、设计需要符合集团规范要求，举证自己参与的部分\n2、重点阐述难度/复杂度的成因及设计考量", "成果要求": ["设计需要符合集团规范要求，举证自己参与的部分", "重点阐述难度/复杂度的成因及设计考量"]}}}, {"活动类别": "设计评审", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "能够作为评委参加小型项目或者中型项目子系统的关键节点技术评审会，能够主导模块级设计评审，识别技术风险，提出改进或优化建议\n【成果要求】\n1、举证识别的技术问题或者风险，提出的评审意见以及价值\n2、举证至少2次评审（至少一次是主导），并被采纳\n3、需要举证提出建议落地的闭环过程", "成果要求": ["举证识别的技术问题或者风险，提出的评审意见以及价值", "举证至少2次评审（至少一次是主导），并被采纳", "需要举证提出建议落地的闭环过程"]}, "四级": {"任职资格标准": "能够作为评委和技术评审负责人参加中型项目的关键节点技术评审会，能够识别技术风险、潜在的可重用技术、组件等，能够提出改进或优化建议\n【成果要求】\n1、举证识别的技术问题或者风险，提出的评审意见以及价值\n2、举证至少2次评审（至少一次是主导），并被采纳\n3、需要举证提出建议落地的闭环过程", "成果要求": ["举证识别的技术问题或者风险，提出的评审意见以及价值", "举证至少2次评审（至少一次是主导），并被采纳", "需要举证提出建议落地的闭环过程"]}, "五级": {"任职资格标准": "能够主导大型项目（或BG/BU级重大项目）的关键节点技术评审工作，能够识别技术风险、潜在的可重用技术、组件等，能够对最终评审结果负责\n【成果要求】\n1、举证识别的技术问题或者风险，提出的评审意见以及价值\n2、举证至少2次评审，并被采纳\n3、需要举证提出建议落地的闭环过程", "成果要求": ["举证识别的技术问题或者风险，提出的评审意见以及价值", "举证至少2次评审，并被采纳", "需要举证提出建议落地的闭环过程"]}}}]}, {"业务单元": "实现与优化", "活动类别": [{"活动类别": "编码", "级别": {"二级": {"任职资格标准": "基于详细设计、编码规范高质量完成模块级的代码编写。\n【成果要求】\n1、需举证遵循的编码规范及规范符合情况。\n2、举证2个及以上模块的代码案例（如版本记录，代码提交记录等）\n3、需举证体现编码质量相关数据或报告（如静态代码分析报告和缺陷率）。\n", "成果要求": ["需举证遵循的编码规范及规范符合情况。", "举证2个及以上模块的代码案例（如版本记录，代码提交记录等）", "需举证体现编码质量相关数据或报告（如静态代码分析报告和缺陷率）。"]}, "三级": {"任职资格标准": "基于详细设计、编码规范高质量完成子系统级代码编写，对编码质量进行度量和持续改进。\n\n【成果要求】\n1、需举证编码范围达到子系统级，以及个人提交记录。\n2、需举证体现编码质量度量和持续改进的过程和成果，如评审报告，质量度量数据比对等。\n3、需举证体现编码质量相关数据或报告（如静态代码分析报告和缺陷率）。", "成果要求": ["需举证编码范围达到子系统级，以及个人提交记录。", "需举证体现编码质量度量和持续改进的过程和成果，如评审报告，质量度量数据比对等。", "需举证体现编码质量相关数据或报告（如静态代码分析报告和缺陷率）。"]}, "四级": {"任职资格标准": "主导中型系统/项目编码实现；负责核心模块的开发；带领团队高效高质量的完成编码工作。\n\n【成果要求】\n1. 举证项目并说明编码实现的主导性。\n2. 举证负责开发的模块。\n3、举证版本质量。", "成果要求": ["举证项目并说明编码实现的主导性。", "举证负责开发的模块。", "举证版本质量。"]}, "五级": {"任职资格标准": "主导大型系统/项目编码实现；负责核心难点模块的开发；指导团队高效高质量的完成核心模块编码工作。\n\n【成果要求】\n1. 举证项目并说明编码实现的主导性。\n2. 举证负责开发的模块。\n3、举证指导的工作。", "成果要求": ["举证项目并说明编码实现的主导性。", "举证负责开发的模块。", "举证指导的工作。"]}}}, {"活动类别": "测试和验证", "级别": {"二级": {"任职资格标准": "1、对模块代码进行功能和性能自测，确保代码满足业务需求和设计要求。\n2、完成一般实验方案的测试和验证，及时发现并解决开发过程中的工具、代码等问题\n\n【成果要求】以上2类活动2选1，举证结果包含下面2个\n1、举证测试与验证的代码范围、边界以及测试验证方法。\n2、需举证测试验证结论以及版本实测的质量报告，并取得良好效果。\n", "成果要求": ["举证测试与验证的代码范围、边界以及测试验证方法。", "需举证测试验证结论以及版本实测的质量报告，并取得良好效果。"]}, "三级": {"任职资格标准": "1、能够对子系统进行复杂场景下的测试与验证，确保代码满足业务需求和设计要求。\n2、至少完成两个方向的实验方案的测试与验证，能够及时发现并解决开发过程中的工具、代码等问题\n\n【成果要求】以上2类活动2选1，举证结果包含下面2个\n1、需举证编码范围达到子系统级\n2、体现场景的复杂性，体现测试与验证方案及评审报告、验证结论\n3、举证单元测试覆盖度", "成果要求": ["需举证编码范围达到子系统级", "体现场景的复杂性，体现测试与验证方案及评审报告、验证结论", "举证单元测试覆盖度"]}, "四级": {"任职资格标准": "参与中/大型项目上线保障测试方案的评审，对关键遗漏项进行补充，保障最终上线质量。\n\n【成果要求】\n1、举证参与的测试方案评审\n2、说明对保障方案的贡献", "成果要求": ["举证参与的测试方案评审", "说明对保障方案的贡献"]}, "五级": {"任职资格标准": "N/A", "成果要求": "N/A"}}}, {"活动类别": "技术攻关", "级别": {"二级": {"任职资格标准": "能够独立完成模块级关键技术问题识别、制定合理目标和解决方案，并完成攻关，取得良好效果。\n【成果要求】\n1、列举对具体的技术挑战，目标与方案\n2、列举攻关过程记录\n3、指标情况（测试报告）", "成果要求": ["列举对具体的技术挑战，目标与方案", "列举攻关过程记录", "指标情况（测试报告）"]}, "三级": {"任职资格标准": "能够主动识别出小型系统或算法引擎/原型的关键技术问题，制定合理目标和解决方案，独立完成和组织团队完成技术攻关任务，取得良好效果。\n【成果要求】\n1、列举具体的技术挑战，目标与方案\n2、列举攻关过程记录\n3、指标情况（测试报告）\n4、至少两例", "成果要求": ["列举具体的技术挑战，目标与方案", "列举攻关过程记录", "指标情况（测试报告）", "至少两例"]}, "四级": {"任职资格标准": "能够主动及时识别出中型系统或复杂引擎中的重大技术挑战，制定合理目标和解决方案，独立完成或组织团队进行有效攻关，取得显著成效，并在业务落地产生价值，相应技术指标不低于竞品\n【成果要求】\n1、列举对具体的技术挑战，目标与方案\n2、列举攻关过程记录\n3、列举应用效果、指标情况（测试报告）\n4、至少两例", "成果要求": ["列举对具体的技术挑战，目标与方案", "列举攻关过程记录", "列举应用效果、指标情况（测试报告）", "至少两例"]}, "五级": {"任职资格标准": "能够主动及时识别出大型系统中的重大技术挑战，制定合理目标和解决方案，组织跨团队联合攻关，取得显著成效，并在业务落地产生重大价值，相应技术指标超过竞品\n【成果要求】\n1、列举对具体的技术挑战，目标与方案\n2、列举攻关过程记录\n3、列举应用效果、指标情况（测试报告）\n4、至少两例", "成果要求": ["列举对具体的技术挑战，目标与方案", "列举攻关过程记录", "列举应用效果、指标情况（测试报告）", "至少两例"]}}}, {"活动类别": "AI工程框架和方法沉淀", "级别": {"二级": {"任职资格标准": "能够对AI应用过程中的代码工具使用和调优方法，进行总结并形成标准化文档；\n或者提炼、沉淀出AI开发工具/组件\n【成果要求】\n1、文档要求5个及以上，工具、组件要求2个及以上", "成果要求": ["文档要求5个及以上，工具、组件要求2个及以上"]}, "三级": {"任职资格标准": "能够提炼或沉淀出可以加速AI系统研发的实施流程/落地流程/工具/框架，在项目组实现复用\n【成果要求】\n1、需举证应用的项目及节省的成本\n2、2个及以上，要求至少一个与AI有直接关系\n3、因为业务需求产生的复用不计入在内\n4、举证流程的，举证的问题必须是影响到研发过程的速度、规范、沟通、质量等方面的相关问题；改进措施需要证明真实落地，并且能够带来速度、质量、规范、沟通等方面的提升；", "成果要求": ["需举证应用的项目及节省的成本", "2个及以上，要求至少一个与AI有直接关系", "因为业务需求产生的复用不计入在内", "举证流程的，举证的问题必须是影响到研发过程的速度、规范、沟通、质量等方面的相关问题；改进措施需要证明真实落地，并且能够带来速度、质量、规范、沟通等方面的提升；"]}, "四级": {"任职资格标准": "能够主动提炼或沉淀出可以加速AI系统研发的工具平台，在产品线级以上的多个项目复用\n【成果要求】\n1、需举证应用的项目及节省的成本\n2、2个及以上，要求至少一个与AI有直接关系\n3、因为业务需求产生的复用不计入在内", "成果要求": ["需举证应用的项目及节省的成本", "2个及以上，要求至少一个与AI有直接关系", "因为业务需求产生的复用不计入在内"]}, "五级": {"任职资格标准": "能够主导规划并指导团队研发出可加速AI系统研发的平台，在实现BG/BU级或以上范围内的复用\n【成果要求】\n1、需举证应用的项目及节省的成本\n2、2个及以上，并与AI生产和落地有直接关系\n3、因为业务需求产生的复用不计入在内", "成果要求": ["需举证应用的项目及节省的成本", "2个及以上，并与AI生产和落地有直接关系", "因为业务需求产生的复用不计入在内"]}}}, {"活动类别": "版本规划与迭代", "级别": {"二级": {"任职资格标准": "N/A", "成果要求": "N/A"}, "三级": {"任职资格标准": "能够在项目演进过程中，制定版本演进计划，协同上下游，按期发布高质量的版本，能够平衡好通用需求和定制需求，合理规划版本，提升主线版本的通用性和扩展性\n说明：对于平台系统确定版本演进计划；对于支撑方案确定工具演进计划；\n【成果要求】\n1、举证一定周期的版本质量\n2、举证为定制需求发布的临时版本，说明主线版本不能快速支持的原因（如特性冲突短时间无法兼容）\n3、举证最终将临时定制版本合并到主线版本的案例（如版本记录），说明解决前期问题的方法", "成果要求": ["举证一定周期的版本质量", "举证为定制需求发布的临时版本，说明主线版本不能快速支持的原因（如特性冲突短时间无法兼容）", "举证最终将临时定制版本合并到主线版本的案例（如版本记录），说明解决前期问题的方法"]}, "四级": {"任职资格标准": "能够在大型项目和复杂系统的演进过程中，制定版本演进计划，协同上下游，按期发布高质量的版本，能够平衡好通用需求和定制需求，合理规划版本，提升主线版本的通用性和扩展性\n【成果要求】\n1、举证一定周期的版本质量\n2、举证为定制需求发布的临时版本，说明主线版本不能快速支持的原因（如特性冲突短时间无法兼容）\n3、举证最终将临时定制版本合并到主线版本的案例（如版本记录），说明解决前期问题的方法", "成果要求": ["举证一定周期的版本质量", "举证为定制需求发布的临时版本，说明主线版本不能快速支持的原因（如特性冲突短时间无法兼容）", "举证最终将临时定制版本合并到主线版本的案例（如版本记录），说明解决前期问题的方法"]}, "五级": {"任职资格标准": "N/A", "成果要求": "N/A"}}}]}, {"技术落地": {"难题排查解决": {"二级": {"职责描述": "针对AI系统模块级问题或AI算法效能问题，能够独立进行问题分析定位和解决方案实施", "成果要求": ["举证参与排查的问题和解决方法", "举证提出的建议，以及后续处理方法"]}, "三级": {"职责描述": "能够主导针对小型AI系统或AI算法引擎/原型应用过程中的疑难问题的系统性分析排查，提出有效解决方案并最终解决，并将优化的产物沉淀，形成解决方案或者经验案例", "成果要求": ["给出疑难问题的分析和解决方法与过程的报告", "沉淀的过程产物（版本迭代、工具包、资源，经验案例等）", "举证类似的问题再次发生时能够快速解决的案例"]}, "四级": {"职责描述": "针对中型AI系统应用过程中的疑难问题，能够主导系统性分析排查，提出解决方案并最终解决；能够规划和主导快速问题分析系统建设，在线上问题处理中取得明显效果", "成果要求": ["给出疑难问题的分析和解决方法与过程的报告", "举证类似的问题再次发生时能够快速解决的案例", "给出主导快速问题分析系统建设的过程材料，说明效果"]}, "五级": {"职责描述": "针对大型和复杂AI系统应用过程中的疑难问题，能够主导系统性分析排查，提出创新性解决方案并最终解决，能够规划和主导大型系统快速问题分析系统建设，在线上问题处理中取得明显效果", "成果要求": ["给出疑难问题的分析和解决方法与过程的报告", "举证类似的问题再次发生时能够快速解决的案例", "给出主导快速问题分析系统建设的过程材料，说明效果"]}}, "外部合作和影响": {"二级": {"职责描述": "熟悉了解AI工程领域职责范围内的主要开源项目，能够参与开源项目建设，并作为某个项目的主要贡献者，为讯飞在业界技术品牌产生正面影响", "成果要求": ["对项目贡献代码超过1000行，影响力可以从社区活跃度、贡献者人数、star数等综合评价", "参与贡献的项目需要是具有业界知名度的项目，举证贡献的解决方案的技术难度"]}, "三级": {"职责描述": "能够主导开发AI工程领域有重要影响力的开源项目，作为第一负责人或对项目做出关键贡献并影响项目走向，项目获得业界广泛认可", "成果要求": ["主导的项目需要是发起者或者核心贡献者，影响力可以从社区活跃度、贡献者人数、star数等综合评价", "参与贡献的项目需要是具有业界知名度的项目，举证贡献的解决方案的技术难度"]}, "四级": "N/A", "五级": "N/A"}}, "落地支撑": {"预案系统建设与风险识别": {"二级": {"职责描述": "能够针对小型项目的生产环境，提前识别出系统风险，在高阶人员有限指导下，完成预案系统建设，并取得良好效果", "成果要求": ["需举证识别的风险，具体的预案措施和取得的效果说明"]}, "三级": {"职责描述": "能够针对中/小型项目的生产环境，提前识别出系统风险，主导完成预案系统建设，并取得良好效果", "成果要求": ["举证识别过程以及主导建设过程以及取得的效果说明，若系统出现重大故障（如P1级别），该项不得分"]}, "四级": {"职责描述": "能够针对大/中型项目的生产环境，提前识别出复杂的系统风险，主导完成预案系统建设，并取得良好效果", "成果要求": ["举证识别过程以及主导建设过程", "举证取得的效果说明", "系统出现重大故障（如P1级别），该项不得分"]}, "五级": {"职责描述": "能够针对大型项目的生产环境，提前识别出关键的系统风险，主导完成预案系统建设，并取得良好效果，总结提炼出研究问题和平台架构需求，推动下一代产品升级", "成果要求": ["举证识别过程以及主导建设过程", "举证取得的效果说明", "系统出现重大故障（如P1级别），该项不得分"]}}, "技术问题解决与竞品分析": {"二级": {"职责描述": "能够主导解决AI应用落地过程中的简单技术问题，并能在过程中分析竞品情况，整理及分析，给出分析报告", "成果要求": ["需要举证问题描述和解决过程，以及业务验收报告、竞品分析报告"]}, "三级": {"职责描述": "能够理解客户需求，主导解决AI应用落地过程中的简单技术问题，并能在过程中分析竞品情况，总结提炼出一般研究性问题或技术需求，推动上下游合作及下一代产品升级", "成果要求": ["举证问题描述和解决过程，以及业务验收报、竞品分析报告，以及被采纳情况"]}, "四级": "N/A", "五级": "N/A"}}}, {"资源整合": {"内部技术合作": {"二级": {"职责描述": "能够负责组织产品线级及以上研发团队内的技术交流和合作，负责跨研发小组的团队资源协调，负责技术合作的目标设定和落地执行，取得良好合作成果", "成果要求": ["强调可以主动发起和组织完成合作，需要其他人配合自己完成研发工作", "提供技术合作所解决的问题、相关涉众、方案、最终成果等"]}, "三级": "N/A", "四级": "N/A", "五级": "N/A"}, "外部合作和影响": {"二级": {"职责描述": "熟悉了解AI工程领域职责范围内的主要开源项目，能够参与开源项目建设，并作为某个项目的主要贡献者，为讯飞在业界技术品牌产生正面影响", "成果要求": ["对项目贡献代码超过1000行，影响力可以从社区活跃度、贡献者人数、star数等综合评价", "参与贡献的项目需要是具有业界知名度的项目，举证贡献的解决方案的技术难度"]}, "三级": {"职责描述": "能够主导开发AI工程领域有重要影响力的开源项目，作为第一负责人或对项目做出关键贡献并影响项目走向，项目获得业界广泛认可", "成果要求": ["主导的项目需要是发起者或者核心贡献者，影响力可以从社区活跃度、贡献者人数、star数等综合评价", "参与贡献的项目需要是具有业界知名度的项目，举证贡献的解决方案的技术难度"]}, "四级": "N/A", "五级": "N/A"}}, "落地支撑": {"预案系统建设与风险识别": {"二级": {"职责描述": "能够针对小型项目的生产环境，提前识别出系统风险，在高阶人员有限指导下，完成预案系统建设，并取得良好效果", "成果要求": ["需举证识别的风险，具体的预案措施和取得的效果说明"]}, "三级": {"职责描述": "能够针对中/小型项目的生产环境，提前识别出系统风险，主导完成预案系统建设，并取得良好效果", "成果要求": ["举证识别过程以及主导建设过程以及取得的效果说明，若系统出现重大故障（如P1级别），该项不得分"]}, "四级": {"职责描述": "能够针对大/中型项目的生产环境，提前识别出复杂的系统风险，主导完成预案系统建设，并取得良好效果", "成果要求": ["举证识别过程以及主导建设过程", "举证取得的效果说明", "系统出现重大故障（如P1级别），该项不得分"]}, "五级": {"职责描述": "能够针对大型项目的生产环境，提前识别出关键的系统风险，主导完成预案系统建设，并取得良好效果，总结提炼出研究问题和平台架构需求，推动下一代产品升级", "成果要求": ["举证识别过程以及主导建设过程", "举证取得的效果说明", "系统出现重大故障（如P1级别），该项不得分"]}}, "技术问题解决与竞品分析": {"二级": {"职责描述": "能够主导解决AI应用落地过程中的简单技术问题，并能在过程中分析竞品情况，整理及分析，给出分析报告", "成果要求": ["需要举证问题描述和解决过程，以及业务验收报告、竞品分析报告"]}, "三级": {"职责描述": "能够理解客户需求，主导解决AI应用落地过程中的简单技术问题，并能在过程中分析竞品情况，总结提炼出一般研究性问题或技术需求，推动上下游合作及下一代产品升级", "成果要求": ["举证问题描述和解决过程，以及业务验收报、竞品分析报告，以及被采纳情况"]}, "四级": "N/A", "五级": "N/A"}}}]}